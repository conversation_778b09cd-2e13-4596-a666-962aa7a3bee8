"use client";

import { useChat } from "ai/react";
import { Paperclip, Send, Copy, ThumbsUp, ThumbsDown } from "lucide-react";

export default function Chat() {
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: "/api/chat",
  });

  return (
    <div className="flex flex-col flex-1">
      {/* Messages */}
      <div className="flex-1 p-6 space-y-6 overflow-y-auto bg-white">
        {messages.map((m, i) => (
          <div key={i} className="flex flex-col gap-2">
            <div className="flex items-start gap-2">
              <span className="font-medium">{m.role === "user" ? "You" : "AI"}</span>
              <p className="bg-neutral-100 px-4 py-2 rounded-lg text-sm max-w-prose">
                {m.content}
              </p>
            </div>
            {m.role !== "user" && (
              <div className="flex gap-3 pl-12 text-neutral-400 text-sm">
                <button className="flex items-center gap-1 hover:text-neutral-700"><Copy size={14} /> Copy</button>
                <button className="flex items-center gap-1 hover:text-green-600"><ThumbsUp size={14} /> </button>
                <button className="flex items-center gap-1 hover:text-red-600"><ThumbsDown size={14} /> </button>
              </div>
            )}
          </div>
        ))}
        {isLoading && <div className="text-neutral-400">AI is typing...</div>}
      </div>

      {/* Input */}
      <form
        onSubmit={handleSubmit}
        className="flex items-center gap-2 border-t p-4 bg-white"
      >
        <button type="button" className="p-2 text-neutral-400 hover:text-neutral-600">
          <Paperclip size={18} />
        </button>
        <input
          className="flex-1 px-4 py-2 rounded-lg border border-neutral-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          value={input}
          onChange={handleInputChange}
          placeholder="Send a message..."
        />
        <button type="submit" className="p-2 text-blue-600 hover:text-blue-800">
          <Send size={18} />
        </button>
      </form>
    </div>
  );
}
