"use client";

import { useState } from "react";
import { Plus, MessageSquare, Settings, User, ChevronDown, MoreHorizontal } from "lucide-react";
import ThemeToggle from "./ThemeToggle";

export default function Sidebar() {
  const [selectedModel, setSelectedModel] = useState("Primary model for all-purpose chat");
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);

  const chatHistory = [
    { id: 1, title: "Hello! How can I help you today?", time: "2m ago" },
    { id: 2, title: "Help with React components", time: "1h ago" },
    { id: 3, title: "JavaScript best practices", time: "3h ago" },
    { id: 4, title: "API integration questions", time: "1d ago" },
  ];

  return (
    <aside className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Chatbot</h1>
          <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
            <Plus size={20} className="text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* Chat Model Selector */}
        <div className="relative">
          <button
            onClick={() => setIsModelDropdownOpen(!isModelDropdownOpen)}
            className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="text-left">
                <div className="text-sm font-medium text-gray-900 dark:text-white">Chat model</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-48">
                  {selectedModel}
                </div>
              </div>
            </div>
            <ChevronDown size={16} className="text-gray-400" />
          </button>

          {isModelDropdownOpen && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10">
              <div className="p-2">
                <button
                  onClick={() => {
                    setSelectedModel("Primary model for all-purpose chat");
                    setIsModelDropdownOpen(false);
                  }}
                  className="w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm"
                >
                  Primary model for all-purpose chat
                </button>
                <button
                  onClick={() => {
                    setSelectedModel("Reasoning model");
                    setIsModelDropdownOpen(false);
                  }}
                  className="w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm"
                >
                  Reasoning model
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Chat History */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">Recent chats</h3>
          <div className="space-y-1">
            {chatHistory.map((chat) => (
              <div
                key={chat.id}
                className="group flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors"
              >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <MessageSquare size={16} className="text-gray-400 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <div className="text-sm text-gray-900 dark:text-white truncate">
                      {chat.title}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {chat.time}
                    </div>
                  </div>
                </div>
                <button className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-all">
                  <MoreHorizontal size={14} className="text-gray-400" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <User size={16} className="text-white" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900 dark:text-white">Guest</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Free plan</div>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <ThemeToggle />
            <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <Settings size={16} className="text-gray-400" />
            </button>
          </div>
        </div>
      </div>
    </aside>
  );
}
