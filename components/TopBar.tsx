"use client";

import { useState } from "react";
import { Plus, MoreHorizontal, ChevronDown, Lock, Globe } from "lucide-react";

export default function TopBar() {
  const [selectedModel, setSelectedModel] = useState("Chat model");
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const [isPrivate, setIsPrivate] = useState(false);
  const [isPrivateDropdownOpen, setIsPrivateDropdownOpen] = useState(false);

  const chatModels = [
    { id: "gpt-4", name: "GPT-4", description: "Most capable model" },
    { id: "gpt-3.5", name: "GPT-3.5 Turbo", description: "Fast and efficient" },
    { id: "claude", name: "<PERSON>", description: "<PERSON><PERSON><PERSON>'s AI assistant" },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Left Section */}
        <div className="flex items-center gap-3">
          {/* Title */}
          <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
            Chatbot
          </h1>

          {/* New Chat Button */}
          <button
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="New Chat"
          >
            <Plus size={18} className="text-gray-600 dark:text-gray-400" />
          </button>

          {/* Menu Button */}
          <button
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Menu"
          >
            <MoreHorizontal size={18} className="text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* Center Section */}
        <div className="flex items-center gap-4">
          {/* Chat Model Selector */}
          <div className="relative">
            <button
              onClick={() => setIsModelDropdownOpen(!isModelDropdownOpen)}
              className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {selectedModel}
              </span>
              <ChevronDown size={16} className="text-gray-500 dark:text-gray-400" />
            </button>

            {isModelDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 dropdown-enter">
                <div className="p-2">
                  {chatModels.map((model) => (
                    <button
                      key={model.id}
                      onClick={() => {
                        setSelectedModel(model.name);
                        setIsModelDropdownOpen(false);
                      }}
                      className="w-full text-left p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    >
                      <div className="font-medium text-gray-900 dark:text-white">
                        {model.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {model.description}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Private Toggle */}
          <div className="relative">
            <button
              onClick={() => setIsPrivateDropdownOpen(!isPrivateDropdownOpen)}
              className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              {isPrivate ? (
                <Lock size={16} className="text-gray-600 dark:text-gray-400" />
              ) : (
                <Globe size={16} className="text-gray-600 dark:text-gray-400" />
              )}
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {isPrivate ? "Private" : "Public"}
              </span>
              <ChevronDown size={16} className="text-gray-500 dark:text-gray-400" />
            </button>

            {isPrivateDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 dropdown-enter">
                <div className="p-2">
                  <button
                    onClick={() => {
                      setIsPrivate(false);
                      setIsPrivateDropdownOpen(false);
                    }}
                    className="w-full text-left p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center gap-3"
                  >
                    <Globe size={16} className="text-gray-600 dark:text-gray-400" />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        Public
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Anyone can view this chat
                      </div>
                    </div>
                  </button>
                  <button
                    onClick={() => {
                      setIsPrivate(true);
                      setIsPrivateDropdownOpen(false);
                    }}
                    className="w-full text-left p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center gap-3"
                  >
                    <Lock size={16} className="text-gray-600 dark:text-gray-400" />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        Private
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Only you can view this chat
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {/* Today indicator */}
          <span className="text-sm text-gray-500 dark:text-gray-400">Today</span>
        </div>
      </div>
    </div>
  );
}
